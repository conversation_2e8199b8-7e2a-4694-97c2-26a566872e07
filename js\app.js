/**
 * Main Application Module
 * Initializes and coordinates all application components
 */

import { CONFIG } from './config.js';
import { DataManager } from './data.js';
import { UIManager } from './ui.js';

class PartsManagementApp {
  constructor() {
    this.dataManager = null;
    this.uiManager = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      console.log('Initializing Parts Management System...');
      
      // Initialize data manager
      this.dataManager = new DataManager();
      
      // Initialize UI manager
      this.uiManager = new UIManager(this.dataManager);
      
      // Make UI manager globally available for onclick handlers
      window.ui = this.uiManager;
      
      // Load initial data
      await this.loadInitialData();
      
      // Setup global error handling
      this.setupErrorHandling();
      
      // Setup performance monitoring if enabled
      if (CONFIG.DEVELOPMENT.ENABLE_PERFORMANCE_MONITORING) {
        this.setupPerformanceMonitoring();
      }
      
      this.isInitialized = true;
      console.log('Parts Management System initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.handleInitializationError(error);
    }
  }

  /**
   * Load initial data
   */
  async loadInitialData() {
    this.uiManager.showLoading();
    
    const success = await this.dataManager.loadData();
    
    if (success) {
      // Populate filters with data
      this.uiManager.populateFilters();
      
      // Apply any saved filters and search
      this.applySavedState();
      
      // Update view mode
      this.uiManager.updateViewMode();
      
      // Render the data
      this.uiManager.render();
      
      console.log(`Loaded ${this.dataManager.parts.length} parts successfully`);
    } else {
      this.uiManager.showError(this.dataManager.lastError);
    }
  }

  /**
   * Apply saved state from localStorage
   */
  applySavedState() {
    // Apply saved search query
    const savedSearch = localStorage.getItem(CONFIG.SEARCH.STORAGE_KEY);
    if (savedSearch) {
      this.dataManager.search(savedSearch);
    }

    // Apply saved filters
    const savedFilters = localStorage.getItem(CONFIG.FILTERS.STORAGE_KEY);
    if (savedFilters) {
      try {
        const filters = JSON.parse(savedFilters);
        if (filters.category) {
          this.dataManager.filterByCategory(filters.category);
        }
        if (filters.status) {
          this.dataManager.filterByStatus(filters.status);
        }
      } catch (error) {
        console.warn('Failed to apply saved filters:', error);
      }
    }
  }

  /**
   * Setup global error handling
   */
  setupErrorHandling() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      if (CONFIG.ERROR_HANDLING.LOG_ERRORS_TO_CONSOLE) {
        console.error('Promise rejection details:', event);
      }
      
      // Prevent the default browser behavior
      event.preventDefault();
      
      // Show user-friendly error message
      this.showErrorToast('An unexpected error occurred. Please try refreshing the page.');
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      console.error('JavaScript error:', event.error);
      
      if (CONFIG.ERROR_HANDLING.LOG_ERRORS_TO_CONSOLE) {
        console.error('Error details:', event);
      }
      
      // Show user-friendly error message for critical errors
      if (event.error && event.error.stack) {
        this.showErrorToast('A technical error occurred. Please try refreshing the page.');
      }
    });
  }

  /**
   * Setup performance monitoring
   */
  setupPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0];
        if (perfData) {
          console.log('Page Load Performance:', {
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
            totalTime: perfData.loadEventEnd - perfData.fetchStart
          });
        }
      }, 0);
    });

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 1000) { // Log slow resources (>1s)
          console.warn('Slow resource detected:', entry.name, `${entry.duration}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * Show error toast notification
   */
  showErrorToast(message) {
    // Create toast element if it doesn't exist
    let toast = document.getElementById('error-toast');
    if (!toast) {
      toast = document.createElement('div');
      toast.id = 'error-toast';
      toast.className = 'error-toast';
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--error-color);
        color: white;
        padding: 1rem;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: var(--z-toast);
        max-width: 400px;
        opacity: 0;
        transform: translateX(100%);
        transition: all var(--transition-normal);
      `;
      document.body.appendChild(toast);
    }

    toast.textContent = message;
    
    // Show toast
    setTimeout(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateX(0)';
    }, 100);

    // Hide toast after duration
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(100%)';
    }, CONFIG.ERROR_HANDLING.ERROR_TOAST_DURATION);
  }

  /**
   * Handle initialization errors
   */
  handleInitializationError(error) {
    const errorContainer = document.createElement('div');
    errorContainer.className = 'initialization-error';
    errorContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--gray-50);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: var(--z-modal);
    `;

    errorContainer.innerHTML = `
      <div style="text-align: center; max-width: 500px; padding: 2rem;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
        <h1 style="color: var(--error-color); margin-bottom: 1rem;">Application Failed to Load</h1>
        <p style="color: var(--gray-600); margin-bottom: 2rem;">
          The Parts Management System could not be initialized. This might be due to a network issue or browser compatibility problem.
        </p>
        <button onclick="window.location.reload()" 
                style="background: var(--primary-color); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: var(--radius-md); cursor: pointer;">
          Reload Page
        </button>
        ${CONFIG.ERROR_HANDLING.SHOW_DETAILED_ERRORS ? `
          <details style="margin-top: 2rem; text-align: left;">
            <summary style="cursor: pointer; color: var(--gray-600);">Technical Details</summary>
            <pre style="background: var(--gray-100); padding: 1rem; border-radius: var(--radius-md); margin-top: 1rem; overflow: auto; font-size: 0.875rem;">${error.stack || error.message}</pre>
          </details>
        ` : ''}
      </div>
    `;

    document.body.appendChild(errorContainer);
  }

  /**
   * Get application statistics
   */
  getStats() {
    if (!this.isInitialized) {
      return { error: 'Application not initialized' };
    }

    return {
      isInitialized: this.isInitialized,
      dataStats: this.dataManager.getStatistics(),
      currentView: this.uiManager.currentView,
      currentPage: this.dataManager.currentPage,
      itemsPerPage: this.dataManager.itemsPerPage,
      searchQuery: this.dataManager.searchQuery,
      filters: this.dataManager.filters,
      performance: {
        partsLoaded: this.dataManager.parts.length,
        filteredResults: this.dataManager.filteredParts.length,
        lastUpdated: localStorage.getItem(CONFIG.DATA.LAST_UPDATED_KEY)
      }
    };
  }

  /**
   * Export application data
   */
  exportAppData(format = 'json') {
    if (!this.isInitialized) {
      throw new Error('Application not initialized');
    }

    return this.dataManager.exportData(format);
  }

  /**
   * Reset application to default state
   */
  reset() {
    if (!this.isInitialized) return;

    // Clear localStorage
    localStorage.removeItem(CONFIG.VIEWS.VIEW_STORAGE_KEY);
    localStorage.removeItem(CONFIG.SEARCH.STORAGE_KEY);
    localStorage.removeItem(CONFIG.FILTERS.STORAGE_KEY);
    localStorage.removeItem(CONFIG.DATA.BACKUP_STORAGE_KEY);
    localStorage.removeItem(CONFIG.DATA.LAST_UPDATED_KEY);

    // Reset data manager
    this.dataManager.clearFilters();
    this.dataManager.currentPage = 1;
    this.dataManager.itemsPerPage = CONFIG.PAGINATION.DEFAULT_ITEMS_PER_PAGE;

    // Reset UI manager
    this.uiManager.currentView = CONFIG.VIEWS.DEFAULT_VIEW;
    this.uiManager.updateViewMode();

    // Reload data
    this.loadInitialData();

    console.log('Application reset to default state');
  }

  /**
   * Check if the application is ready
   */
  isReady() {
    return this.isInitialized && 
           this.dataManager && 
           this.uiManager && 
           !this.dataManager.isLoading;
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  const app = new PartsManagementApp();
  
  // Make app globally available for debugging
  window.partsApp = app;
  
  // Initialize the application
  await app.init();
});

// Export for module usage
export default PartsManagementApp;
