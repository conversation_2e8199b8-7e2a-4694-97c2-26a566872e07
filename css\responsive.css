/* ===== RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* ===== BREAKPOINTS ===== */
/* 
  Mobile: 320px - 767px (default)
  Tablet: 768px - 1023px
  Desktop: 1024px+
*/

/* ===== MOBILE STYLES (DEFAULT) ===== */
/* Base styles are already mobile-first in styles.css */

.container {
  padding: 0 var(--spacing-sm);
}

/* Header adjustments for mobile */
.header-content {
  flex-direction: column;
  height: auto;
  padding: var(--spacing-md) 0;
  gap: var(--spacing-md);
}

.nav {
  width: 100%;
  flex-direction: column;
  gap: var(--spacing-md);
}

.nav-actions {
  width: 100%;
  justify-content: center;
}

.view-toggle {
  width: 100%;
  justify-content: center;
}

/* Search section mobile */
.search-controls {
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search-input-wrapper {
  min-width: auto;
}

.filter-controls {
  width: 100%;
  justify-content: space-between;
}

.filter-select {
  flex: 1;
}

/* Data display mobile adjustments */
.data-display.grid-view {
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.data-display.card-view {
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.part-list-item {
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  text-align: center;
}

.part-list-meta {
  justify-content: center;
  flex-wrap: wrap;
}

/* Compact view - horizontal scroll on mobile */
.data-display.compact-view {
  overflow-x: auto;
}

.parts-table {
  min-width: 600px;
}

/* Pagination mobile */
.pagination-container {
  flex-direction: column;
  gap: var(--spacing-md);
  text-align: center;
}

.pagination-controls {
  justify-content: center;
  flex-wrap: wrap;
}

.page-numbers {
  flex-wrap: wrap;
  justify-content: center;
}

/* FAB adjustments */
.fab-container {
  bottom: var(--spacing-md);
  right: var(--spacing-md);
}

.fab-item-label {
  display: none;
}

.fab-item {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  justify-content: center;
  border-radius: 50%;
}

/* Modal mobile */
.modal-overlay {
  padding: var(--spacing-sm);
}

.modal {
  max-height: 95vh;
}

.modal-body {
  max-height: 50vh;
}

.form-row {
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

/* ===== TABLET STYLES ===== */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  /* Header tablet */
  .header-content {
    flex-direction: row;
    height: var(--header-height);
    padding: 0;
  }

  .nav {
    flex-direction: row;
    width: auto;
  }

  .nav-actions {
    width: auto;
  }

  .view-toggle {
    width: auto;
  }

  /* Search section tablet */
  .search-controls {
    flex-direction: row;
    align-items: center;
  }

  .search-input-wrapper {
    min-width: 300px;
  }

  .filter-controls {
    width: auto;
  }

  .filter-select {
    flex: none;
  }

  /* Data display tablet */
  .data-display.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .data-display.card-view {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }

  .part-list-item {
    grid-template-columns: auto 1fr auto;
    text-align: left;
  }

  .part-list-meta {
    justify-content: flex-start;
    flex-wrap: nowrap;
  }

  /* Pagination tablet */
  .pagination-container {
    flex-direction: row;
    text-align: left;
  }

  .pagination-controls {
    flex-wrap: nowrap;
  }

  .page-numbers {
    flex-wrap: nowrap;
  }

  /* FAB tablet */
  .fab-item-label {
    display: block;
  }

  .fab-item {
    width: auto;
    height: auto;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
  }

  /* Modal tablet */
  .modal-overlay {
    padding: var(--spacing-lg);
  }

  .modal-body {
    max-height: 60vh;
  }

  .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

/* ===== DESKTOP STYLES ===== */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-lg);
  }

  /* Data display desktop */
  .data-display.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .data-display.card-view {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  /* FAB desktop */
  .fab-container {
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
  }

  /* Modal desktop */
  .modal {
    max-width: 600px;
  }
}

/* ===== LARGE DESKTOP STYLES ===== */
@media (min-width: 1200px) {
  .data-display.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .data-display.card-view {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .search-section,
  .footer,
  .fab-container,
  .modal-overlay {
    display: none !important;
  }

  .main {
    padding: 0;
  }

  .data-display {
    display: block !important;
  }

  .part-card,
  .part-list-item,
  .part-card-large {
    break-inside: avoid;
    margin-bottom: var(--spacing-md);
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
  }

  .parts-table {
    font-size: var(--font-size-xs);
  }

  .parts-table th,
  .parts-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .loading-spinner {
    animation: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #f0f0f0;
    --gray-200: #d0d0d0;
    --gray-300: #b0b0b0;
    --gray-800: #2a2a2a;
    --gray-900: #1a1a1a;
  }

  .part-card,
  .part-list-item,
  .part-card-large {
    border-width: 2px;
  }

  .btn {
    border-width: 2px;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1a1a1a;
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }

  .loading-spinner {
    border-color: var(--gray-600);
    border-top-color: var(--primary-color);
  }
}
