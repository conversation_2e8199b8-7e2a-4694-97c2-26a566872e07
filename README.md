# Parts Management System

A modern, responsive web application for managing parts inventory with multiple view modes, advanced filtering, and comprehensive data management capabilities.

## Features

### 🎯 Core Functionality
- **Multiple View Modes**: Grid, List, Card, and Compact table views
- **Advanced Search**: Real-time search across multiple fields
- **Smart Filtering**: Filter by category, status, and other criteria
- **Pagination**: Configurable items per page with intuitive navigation
- **Data Export**: Export filtered data in JSON or CSV formats
- **CRUD Operations**: Add, edit, and delete parts with form validation

### 🎨 User Interface
- **Modern Design**: Clean, professional interface with consistent styling
- **Responsive Layout**: Mobile-first design that works on all devices
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Dark Mode**: Automatic dark mode support based on system preferences
- **Floating Action Button**: Quick access to common actions with speed dial menu
- **Loading States**: Smooth loading animations and error handling

### ⚡ Performance
- **Fast Rendering**: Optimized rendering for large datasets
- **Local Storage**: Automatic backup and preference saving
- **Debounced Search**: Efficient search with configurable debounce timing
- **Lazy Loading**: Performance optimizations for better user experience

## Technology Stack

- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern CSS with custom properties, Grid, and Flexbox
- **JavaScript ES6+**: Modular architecture with async/await
- **No Frameworks**: Pure vanilla JavaScript for maximum performance

## Project Structure

```
parts-management-system/
├── index.html              # Main HTML file
├── css/
│   ├── styles.css          # Main stylesheet with CSS variables
│   └── responsive.css      # Responsive design and media queries
├── js/
│   ├── config.js          # Application configuration
│   ├── data.js            # Data management module
│   ├── ui.js              # UI rendering and interaction module
│   └── app.js             # Main application initialization
├── data/
│   └── parts.json         # Sample parts data
└── README.md              # This file
```

## Getting Started

### Prerequisites
- Modern web browser (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- Local web server (for development)

### Installation

1. **Clone or download** the project files to your local machine

2. **Start a local web server** (required for loading JSON data):
   
   **Using Python:**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```
   
   **Using Node.js:**
   ```bash
   npx http-server
   ```
   
   **Using PHP:**
   ```bash
   php -S localhost:8000
   ```

3. **Open your browser** and navigate to `http://localhost:8000`

## Configuration

The application is highly configurable through the `js/config.js` file. Key configuration options include:

### Data Settings
```javascript
DATA: {
  JSON_FILE_PATH: 'data/parts.json',
  AUTO_SAVE_INTERVAL: 30000,
  MAX_RETRY_ATTEMPTS: 3
}
```

### Pagination Settings
```javascript
PAGINATION: {
  DEFAULT_ITEMS_PER_PAGE: 25,
  ITEMS_PER_PAGE_OPTIONS: [10, 25, 50, 100],
  MAX_PAGE_NUMBERS_SHOWN: 5
}
```

### Search Settings
```javascript
SEARCH: {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2,
  SEARCH_FIELDS: ['partId', 'description', 'category', 'functionGroup']
}
```

## Usage

### Navigation
- **Add Button**: Create new parts with the modal form
- **Export Button**: Download current data as JSON or CSV
- **Refresh Button**: Reload data from the server
- **View Toggle**: Switch between Grid, List, Card, and Compact views

### Search and Filtering
- **Search Bar**: Type to search across part ID, description, category, and function group
- **Category Filter**: Filter parts by specific categories
- **Status Filter**: Show only active or inactive parts
- **Clear Filters**: Reset all filters and search criteria

### View Modes

#### Grid View
- Card-based layout with key information
- Responsive grid that adapts to screen size
- Hover effects and status badges

#### List View
- Vertical list with detailed information
- Optimized for scanning large amounts of data
- Action buttons for each item

#### Card View
- Large cards with comprehensive details
- Enhanced visual presentation
- Perfect for detailed part review

#### Compact View
- Table format for maximum data density
- Sortable columns
- Horizontal scrolling on mobile devices

### Keyboard Shortcuts
- `Ctrl+F`: Focus search input
- `Ctrl+N`: Open add new part modal
- `Ctrl+E`: Export data
- `F5`: Refresh data
- `Escape`: Close modal or clear focus

## Data Format

Parts data should follow this JSON structure:

```json
{
  "parts": [
    {
      "id": "001729",
      "partId": "001729",
      "description": "SHAFT ASSY, IDLER, W/S WIPER",
      "category": "AUT-Classic/New",
      "functionGroup": "Each",
      "uom": "Each",
      "isComponent": false,
      "isActive": true,
      "dateCreated": "2024-01-15T10:30:00Z",
      "lastModified": "2024-01-15T10:30:00Z"
    }
  ],
  "metadata": {
    "totalCount": 50,
    "lastUpdated": "2024-03-08T13:35:00Z",
    "version": "1.0.0"
  }
}
```

### Required Fields
- `id`: Unique identifier
- `partId`: Part number/ID
- `description`: Part description
- `category`: Part category

### Optional Fields
- `functionGroup`: Functional grouping
- `uom`: Unit of measure
- `isComponent`: Boolean indicating if it's a component
- `isActive`: Boolean indicating if the part is active
- `dateCreated`: ISO date string
- `lastModified`: ISO date string

## Customization

### Styling
The application uses CSS custom properties for easy theming. Key variables are defined in `css/styles.css`:

```css
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  /* ... more variables */
}
```

### Adding New Categories
Update the `CATEGORIES` array in `js/config.js`:

```javascript
CATEGORIES: [
  'AUT-Classic/New',
  'LFS 96-2000',
  'LFS 2000-2007',
  'Engine',
  'ZBUY TOBE DETE',
  'Your New Category'
]
```

## Browser Support

- **Chrome**: 80+
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

## Performance Considerations

- The application is optimized for datasets up to 10,000 parts
- Virtual scrolling is automatically enabled for datasets over 1,000 items
- Search results are cached for improved performance
- Images and resources are lazy-loaded when applicable

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: ARIA labels and live regions
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user's motion preferences
- **Focus Management**: Proper focus handling in modals and navigation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly across different browsers and devices
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the project repository or contact the development team.

---

**Parts Management System v1.0.0**  
Built with ❤️ using modern web technologies
