/**
 * Data Management Module
 * Handles all data operations including loading, saving, filtering, and searching
 */

import { CONFIG } from './config.js';

export class DataManager {
  constructor() {
    this.parts = [];
    this.filteredParts = [];
    this.metadata = {};
    this.currentPage = 1;
    this.itemsPerPage = CONFIG.PAGINATION.DEFAULT_ITEMS_PER_PAGE;
    this.searchQuery = '';
    this.filters = {
      category: '',
      status: ''
    };
    this.sortConfig = {
      field: 'partId',
      direction: 'asc'
    };
    this.isLoading = false;
    this.lastError = null;
  }

  /**
   * Load parts data from JSON file
   * @returns {Promise<boolean>} Success status
   */
  async loadData() {
    this.isLoading = true;
    this.lastError = null;

    try {
      // Show minimum loading time for better UX
      const loadPromise = this.fetchPartsData();
      const minTimePromise = new Promise(resolve => 
        setTimeout(resolve, CONFIG.UI.LOADING_MIN_DURATION)
      );

      const [data] = await Promise.all([loadPromise, minTimePromise]);
      
      this.parts = data.parts || [];
      this.metadata = data.metadata || {};
      this.filteredParts = [...this.parts];
      
      // Save backup to localStorage
      this.saveBackup();
      
      this.isLoading = false;
      return true;
    } catch (error) {
      console.error('Error loading data:', error);
      this.lastError = error.message;
      
      // Try to load from backup
      if (CONFIG.ERROR_HANDLING.FALLBACK_TO_LOCAL_STORAGE) {
        const backupLoaded = this.loadFromBackup();
        if (backupLoaded) {
          this.isLoading = false;
          return true;
        }
      }
      
      this.isLoading = false;
      return false;
    }
  }

  /**
   * Fetch parts data from JSON file
   * @returns {Promise<Object>} Parts data
   */
  async fetchPartsData() {
    const response = await fetch(CONFIG.DATA.JSON_FILE_PATH);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  }

  /**
   * Save data backup to localStorage
   */
  saveBackup() {
    try {
      const backupData = {
        parts: this.parts,
        metadata: this.metadata,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(CONFIG.DATA.BACKUP_STORAGE_KEY, JSON.stringify(backupData));
      localStorage.setItem(CONFIG.DATA.LAST_UPDATED_KEY, backupData.timestamp);
    } catch (error) {
      console.warn('Failed to save backup to localStorage:', error);
    }
  }

  /**
   * Load data from localStorage backup
   * @returns {boolean} Success status
   */
  loadFromBackup() {
    try {
      const backupData = localStorage.getItem(CONFIG.DATA.BACKUP_STORAGE_KEY);
      if (!backupData) return false;

      const data = JSON.parse(backupData);
      this.parts = data.parts || [];
      this.metadata = data.metadata || {};
      this.filteredParts = [...this.parts];
      
      console.info('Loaded data from backup');
      return true;
    } catch (error) {
      console.warn('Failed to load backup from localStorage:', error);
      return false;
    }
  }

  /**
   * Search parts by query
   * @param {string} query - Search query
   */
  search(query) {
    this.searchQuery = query.toLowerCase().trim();
    this.currentPage = 1; // Reset to first page
    this.applyFilters();
  }

  /**
   * Apply category filter
   * @param {string} category - Category to filter by
   */
  filterByCategory(category) {
    this.filters.category = category;
    this.currentPage = 1;
    this.applyFilters();
  }

  /**
   * Apply status filter
   * @param {string} status - Status to filter by ('active', 'inactive', or '')
   */
  filterByStatus(status) {
    this.filters.status = status;
    this.currentPage = 1;
    this.applyFilters();
  }

  /**
   * Clear all filters and search
   */
  clearFilters() {
    this.searchQuery = '';
    this.filters = { category: '', status: '' };
    this.currentPage = 1;
    this.applyFilters();
  }

  /**
   * Apply all filters and search
   */
  applyFilters() {
    let filtered = [...this.parts];

    // Apply search filter
    if (this.searchQuery && this.searchQuery.length >= CONFIG.SEARCH.MIN_SEARCH_LENGTH) {
      filtered = filtered.filter(part => {
        return CONFIG.SEARCH.SEARCH_FIELDS.some(field => {
          const value = part[field];
          if (!value) return false;
          
          const searchValue = CONFIG.SEARCH.CASE_SENSITIVE 
            ? value.toString() 
            : value.toString().toLowerCase();
          
          return searchValue.includes(this.searchQuery);
        });
      });
    }

    // Apply category filter
    if (this.filters.category) {
      filtered = filtered.filter(part => part.category === this.filters.category);
    }

    // Apply status filter
    if (this.filters.status) {
      const isActive = this.filters.status === 'active';
      filtered = filtered.filter(part => part.isActive === isActive);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[this.sortConfig.field];
      const bValue = b[this.sortConfig.field];
      
      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      if (aValue > bValue) comparison = 1;
      
      return this.sortConfig.direction === 'desc' ? -comparison : comparison;
    });

    this.filteredParts = filtered;
  }

  /**
   * Sort parts by field
   * @param {string} field - Field to sort by
   * @param {string} direction - Sort direction ('asc' or 'desc')
   */
  sort(field, direction = 'asc') {
    this.sortConfig = { field, direction };
    this.applyFilters();
  }

  /**
   * Set items per page
   * @param {number} itemsPerPage - Number of items per page
   */
  setItemsPerPage(itemsPerPage) {
    this.itemsPerPage = itemsPerPage;
    this.currentPage = 1;
  }

  /**
   * Go to specific page
   * @param {number} page - Page number
   */
  goToPage(page) {
    const totalPages = this.getTotalPages();
    if (page >= 1 && page <= totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Get current page data
   * @returns {Array} Current page parts
   */
  getCurrentPageData() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredParts.slice(startIndex, endIndex);
  }

  /**
   * Get total number of pages
   * @returns {number} Total pages
   */
  getTotalPages() {
    return Math.ceil(this.filteredParts.length / this.itemsPerPage);
  }

  /**
   * Get pagination info
   * @returns {Object} Pagination information
   */
  getPaginationInfo() {
    const totalItems = this.filteredParts.length;
    const totalPages = this.getTotalPages();
    const startItem = totalItems === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
    const endItem = Math.min(this.currentPage * this.itemsPerPage, totalItems);

    return {
      currentPage: this.currentPage,
      totalPages,
      totalItems,
      startItem,
      endItem,
      itemsPerPage: this.itemsPerPage,
      hasNextPage: this.currentPage < totalPages,
      hasPrevPage: this.currentPage > 1
    };
  }

  /**
   * Add new part
   * @param {Object} partData - Part data
   * @returns {boolean} Success status
   */
  addPart(partData) {
    try {
      // Validate required fields
      if (!partData.partId || !partData.description || !partData.category) {
        throw new Error('Missing required fields');
      }

      // Check for duplicate part ID
      if (this.parts.some(part => part.partId === partData.partId)) {
        throw new Error('Part ID already exists');
      }

      const newPart = {
        id: partData.partId, // Use partId as id for simplicity
        ...partData,
        dateCreated: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      this.parts.push(newPart);
      this.applyFilters();
      this.saveBackup();
      
      return true;
    } catch (error) {
      console.error('Error adding part:', error);
      this.lastError = error.message;
      return false;
    }
  }

  /**
   * Update existing part
   * @param {string} partId - Part ID to update
   * @param {Object} partData - Updated part data
   * @returns {boolean} Success status
   */
  updatePart(partId, partData) {
    try {
      const index = this.parts.findIndex(part => part.partId === partId);
      if (index === -1) {
        throw new Error('Part not found');
      }

      this.parts[index] = {
        ...this.parts[index],
        ...partData,
        lastModified: new Date().toISOString()
      };

      this.applyFilters();
      this.saveBackup();
      
      return true;
    } catch (error) {
      console.error('Error updating part:', error);
      this.lastError = error.message;
      return false;
    }
  }

  /**
   * Delete part
   * @param {string} partId - Part ID to delete
   * @returns {boolean} Success status
   */
  deletePart(partId) {
    try {
      const index = this.parts.findIndex(part => part.partId === partId);
      if (index === -1) {
        throw new Error('Part not found');
      }

      this.parts.splice(index, 1);
      this.applyFilters();
      this.saveBackup();
      
      return true;
    } catch (error) {
      console.error('Error deleting part:', error);
      this.lastError = error.message;
      return false;
    }
  }

  /**
   * Get unique categories
   * @returns {Array} Array of unique categories
   */
  getCategories() {
    const categories = [...new Set(this.parts.map(part => part.category))];
    return categories.filter(Boolean).sort();
  }

  /**
   * Get unique function groups
   * @returns {Array} Array of unique function groups
   */
  getFunctionGroups() {
    const groups = [...new Set(this.parts.map(part => part.functionGroup))];
    return groups.filter(Boolean).sort();
  }

  /**
   * Export data in specified format
   * @param {string} format - Export format ('json', 'csv')
   * @returns {string} Exported data
   */
  exportData(format = 'json') {
    const data = this.filteredParts.length > 0 ? this.filteredParts : this.parts;
    
    switch (format.toLowerCase()) {
      case 'csv':
        return this.exportToCSV(data);
      case 'json':
      default:
        return this.exportToJSON(data);
    }
  }

  /**
   * Export data to JSON format
   * @param {Array} data - Data to export
   * @returns {string} JSON string
   */
  exportToJSON(data) {
    const exportData = {
      parts: data,
      metadata: {
        ...this.metadata,
        exportDate: new Date().toISOString(),
        totalCount: data.length,
        filters: this.filters,
        searchQuery: this.searchQuery
      }
    };
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export data to CSV format
   * @param {Array} data - Data to export
   * @returns {string} CSV string
   */
  exportToCSV(data) {
    if (data.length === 0) return '';

    const headers = ['Part ID', 'Description', 'Category', 'Function Group', 'UOM', 'Is Component', 'Is Active'];
    const csvRows = [headers.join(CONFIG.EXPORT.CSV_DELIMITER)];

    data.forEach(part => {
      const row = [
        this.escapeCSVField(part.partId),
        this.escapeCSVField(part.description),
        this.escapeCSVField(part.category),
        this.escapeCSVField(part.functionGroup),
        this.escapeCSVField(part.uom),
        part.isComponent ? 'Yes' : 'No',
        part.isActive ? 'Yes' : 'No'
      ];
      csvRows.push(row.join(CONFIG.EXPORT.CSV_DELIMITER));
    });

    return csvRows.join('\n');
  }

  /**
   * Escape CSV field
   * @param {string} field - Field to escape
   * @returns {string} Escaped field
   */
  escapeCSVField(field) {
    if (!field) return '';
    const stringField = field.toString();
    if (stringField.includes(CONFIG.EXPORT.CSV_DELIMITER) || 
        stringField.includes(CONFIG.EXPORT.CSV_QUOTE_CHAR) || 
        stringField.includes('\n')) {
      return `${CONFIG.EXPORT.CSV_QUOTE_CHAR}${stringField.replace(/"/g, '""')}${CONFIG.EXPORT.CSV_QUOTE_CHAR}`;
    }
    return stringField;
  }

  /**
   * Get statistics
   * @returns {Object} Data statistics
   */
  getStatistics() {
    const total = this.parts.length;
    const active = this.parts.filter(part => part.isActive).length;
    const inactive = total - active;
    const components = this.parts.filter(part => part.isComponent).length;
    const categories = this.getCategories().length;

    return {
      total,
      active,
      inactive,
      components,
      categories,
      filtered: this.filteredParts.length
    };
  }
}
