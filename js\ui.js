/**
 * UI Management Module
 * Handles all UI rendering and interactions
 */

import { CONFIG } from './config.js';

export class UIManager {
  constructor(dataManager) {
    this.dataManager = dataManager;
    this.currentView = CONFIG.VIEWS.DEFAULT_VIEW;
    this.elements = {};
    this.debounceTimers = {};
    this.isModalOpen = false;
    this.isFabOpen = false;

    this.initializeElements();
    this.setupEventListeners();
    this.loadUserPreferences();
  }

  /**
   * Initialize DOM element references
   */
  initializeElements() {
    this.elements = {
      // States
      loadingState: document.getElementById('loading-state'),
      errorState: document.getElementById('error-state'),
      emptyState: document.getElementById('empty-state'),
      dataContainer: document.getElementById('data-container'),

      // Search and filters
      searchInput: document.getElementById('search-input'),
      searchClear: document.getElementById('search-clear'),
      categoryFilter: document.getElementById('category-filter'),
      statusFilter: document.getElementById('status-filter'),
      clearFiltersBtn: document.getElementById('clear-filters-btn'),

      // View controls
      viewButtons: document.querySelectorAll('.view-btn'),
      dataDisplay: document.getElementById('data-display'),
      resultsInfo: document.getElementById('results-info'),

      // Navigation
      addBtn: document.getElementById('add-btn'),
      exportBtn: document.getElementById('export-btn'),
      refreshBtn: document.getElementById('refresh-btn'),
      retryBtn: document.getElementById('retry-btn'),

      // Pagination
      itemsPerPage: document.getElementById('items-per-page'),
      paginationControls: document.getElementById('pagination-controls'),
      pageNumbers: document.getElementById('page-numbers'),
      paginationSummary: document.getElementById('pagination-summary'),
      firstPageBtn: document.getElementById('first-page'),
      prevPageBtn: document.getElementById('prev-page'),
      nextPageBtn: document.getElementById('next-page'),
      lastPageBtn: document.getElementById('last-page'),

      // FAB
      fabContainer: document.getElementById('fab-container'),
      fabMain: document.getElementById('fab-main'),
      fabMenu: document.getElementById('fab-menu'),

      // Modal
      modalOverlay: document.getElementById('modal-overlay'),
      modal: document.querySelector('.modal'),
      modalTitle: document.getElementById('modal-title'),
      modalClose: document.getElementById('modal-close'),
      modalCancel: document.getElementById('modal-cancel'),
      modalSave: document.getElementById('modal-save'),
      partForm: document.getElementById('part-form'),

      // Form fields
      partId: document.getElementById('part-id'),
      partDescription: document.getElementById('part-description'),
      partCategory: document.getElementById('part-category'),
      partUom: document.getElementById('part-uom'),
      partFunctionGroup: document.getElementById('part-function-group'),
      isComponent: document.getElementById('is-component'),
      isActive: document.getElementById('is-active')
    };
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Search
    this.elements.searchInput?.addEventListener('input', (e) => {
      this.debounce('search', () => {
        this.handleSearch(e.target.value);
      }, CONFIG.SEARCH.DEBOUNCE_DELAY);
    });

    this.elements.searchClear?.addEventListener('click', () => {
      this.clearSearch();
    });

    // Filters
    this.elements.categoryFilter?.addEventListener('change', (e) => {
      this.handleCategoryFilter(e.target.value);
    });

    this.elements.statusFilter?.addEventListener('change', (e) => {
      this.handleStatusFilter(e.target.value);
    });

    this.elements.clearFiltersBtn?.addEventListener('click', () => {
      this.clearAllFilters();
    });

    // View buttons
    this.elements.viewButtons?.forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleViewChange(e.target.dataset.view);
      });
    });

    // Navigation buttons
    this.elements.addBtn?.addEventListener('click', () => {
      this.openAddModal();
    });

    this.elements.exportBtn?.addEventListener('click', () => {
      this.handleExport();
    });

    this.elements.refreshBtn?.addEventListener('click', () => {
      this.handleRefresh();
    });

    this.elements.retryBtn?.addEventListener('click', () => {
      this.handleRefresh();
    });

    // Pagination
    this.elements.itemsPerPage?.addEventListener('change', (e) => {
      this.handleItemsPerPageChange(parseInt(e.target.value));
    });

    this.elements.firstPageBtn?.addEventListener('click', () => {
      this.goToPage(1);
    });

    this.elements.prevPageBtn?.addEventListener('click', () => {
      this.goToPage(this.dataManager.currentPage - 1);
    });

    this.elements.nextPageBtn?.addEventListener('click', () => {
      this.goToPage(this.dataManager.currentPage + 1);
    });

    this.elements.lastPageBtn?.addEventListener('click', () => {
      this.goToPage(this.dataManager.getTotalPages());
    });

    // FAB
    this.elements.fabMain?.addEventListener('click', () => {
      this.toggleFab();
    });

    // FAB menu items
    document.querySelectorAll('.fab-item').forEach(item => {
      item.addEventListener('click', (e) => {
        this.handleFabAction(e.currentTarget.dataset.action);
      });
    });

    // Modal
    this.elements.modalClose?.addEventListener('click', () => {
      this.closeModal();
    });

    this.elements.modalCancel?.addEventListener('click', () => {
      this.closeModal();
    });

    this.elements.modalOverlay?.addEventListener('click', (e) => {
      if (e.target === this.elements.modalOverlay) {
        this.closeModal();
      }
    });

    // Form submission
    this.elements.partForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleFormSubmit();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Window events
    window.addEventListener('resize', () => {
      this.debounce('resize', () => {
        this.handleResize();
      }, CONFIG.PERFORMANCE.DEBOUNCE_RESIZE);
    });
  }

  /**
   * Debounce function calls
   */
  debounce(key, func, delay) {
    clearTimeout(this.debounceTimers[key]);
    this.debounceTimers[key] = setTimeout(func, delay);
  }

  /**
   * Load user preferences from localStorage
   */
  loadUserPreferences() {
    // Load preferred view mode
    const savedView = localStorage.getItem(CONFIG.VIEWS.VIEW_STORAGE_KEY);
    if (savedView && CONFIG.VIEWS.AVAILABLE_VIEWS.includes(savedView)) {
      this.currentView = savedView;
    }

    // Load last search query
    const savedSearch = localStorage.getItem(CONFIG.SEARCH.STORAGE_KEY);
    if (savedSearch && this.elements.searchInput) {
      this.elements.searchInput.value = savedSearch;
    }

    // Load applied filters
    const savedFilters = localStorage.getItem(CONFIG.FILTERS.STORAGE_KEY);
    if (savedFilters) {
      try {
        const filters = JSON.parse(savedFilters);
        if (filters.category && this.elements.categoryFilter) {
          this.elements.categoryFilter.value = filters.category;
        }
        if (filters.status && this.elements.statusFilter) {
          this.elements.statusFilter.value = filters.status;
        }
      } catch (error) {
        console.warn('Failed to load saved filters:', error);
      }
    }
  }

  /**
   * Save user preferences to localStorage
   */
  saveUserPreferences() {
    localStorage.setItem(CONFIG.VIEWS.VIEW_STORAGE_KEY, this.currentView);

    if (this.elements.searchInput?.value) {
      localStorage.setItem(CONFIG.SEARCH.STORAGE_KEY, this.elements.searchInput.value);
    }

    const filters = {
      category: this.elements.categoryFilter?.value || '',
      status: this.elements.statusFilter?.value || ''
    };
    localStorage.setItem(CONFIG.FILTERS.STORAGE_KEY, JSON.stringify(filters));
  }

  /**
   * Show loading state
   */
  showLoading() {
    this.hideAllStates();
    this.elements.loadingState?.removeAttribute('hidden');
  }

  /**
   * Show error state
   */
  showError(message) {
    this.hideAllStates();
    this.elements.errorState?.removeAttribute('hidden');
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) {
      errorMessage.textContent = message || 'An error occurred while loading data.';
    }
  }

  /**
   * Show empty state
   */
  showEmpty() {
    this.hideAllStates();
    this.elements.emptyState?.removeAttribute('hidden');
  }

  /**
   * Show data container
   */
  showData() {
    this.hideAllStates();
    this.elements.dataContainer?.removeAttribute('hidden');
  }

  /**
   * Hide all state containers
   */
  hideAllStates() {
    this.elements.loadingState?.setAttribute('hidden', '');
    this.elements.errorState?.setAttribute('hidden', '');
    this.elements.emptyState?.setAttribute('hidden', '');
    this.elements.dataContainer?.setAttribute('hidden', '');
  }

  /**
   * Update view mode
   */
  updateViewMode() {
    // Update view buttons
    this.elements.viewButtons?.forEach(btn => {
      const isActive = btn.dataset.view === this.currentView;
      btn.classList.toggle('active', isActive);
      btn.setAttribute('aria-pressed', isActive.toString());
    });

    // Update data display class
    if (this.elements.dataDisplay) {
      this.elements.dataDisplay.className = `data-display ${this.currentView}-view`;
    }

    this.saveUserPreferences();
  }

  /**
   * Populate category filter options
   */
  populateFilters() {
    if (this.elements.categoryFilter) {
      const categories = this.dataManager.getCategories();
      const currentValue = this.elements.categoryFilter.value;

      // Clear existing options except the first one
      while (this.elements.categoryFilter.children.length > 1) {
        this.elements.categoryFilter.removeChild(this.elements.categoryFilter.lastChild);
      }

      // Add category options
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        this.elements.categoryFilter.appendChild(option);
      });

      // Restore previous value if it still exists
      if (currentValue && categories.includes(currentValue)) {
        this.elements.categoryFilter.value = currentValue;
      }
    }

    // Populate form category options
    if (this.elements.partCategory) {
      const categories = this.dataManager.getCategories();
      const currentValue = this.elements.partCategory.value;

      // Clear existing options except the first one
      while (this.elements.partCategory.children.length > 1) {
        this.elements.partCategory.removeChild(this.elements.partCategory.lastChild);
      }

      // Add category options
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        this.elements.partCategory.appendChild(option);
      });

      // Add config categories if not already present
      CONFIG.CATEGORIES.forEach(category => {
        if (!categories.includes(category)) {
          const option = document.createElement('option');
          option.value = category;
          option.textContent = category;
          this.elements.partCategory.appendChild(option);
        }
      });

      if (currentValue) {
        this.elements.partCategory.value = currentValue;
      }
    }
  }

  /**
   * Update search clear button visibility
   */
  updateSearchClear() {
    if (this.elements.searchClear && this.elements.searchInput) {
      const hasValue = this.elements.searchInput.value.length > 0;
      this.elements.searchClear.hidden = !hasValue;
    }
  }

  /**
   * Handle search input
   */
  handleSearch(query) {
    this.dataManager.search(query);
    this.updateSearchClear();
    this.render();
    this.saveUserPreferences();
  }

  /**
   * Clear search
   */
  clearSearch() {
    if (this.elements.searchInput) {
      this.elements.searchInput.value = '';
      this.handleSearch('');
    }
  }

  /**
   * Handle category filter change
   */
  handleCategoryFilter(category) {
    this.dataManager.filterByCategory(category);
    this.render();
    this.saveUserPreferences();
  }

  /**
   * Handle status filter change
   */
  handleStatusFilter(status) {
    this.dataManager.filterByStatus(status);
    this.render();
    this.saveUserPreferences();
  }

  /**
   * Clear all filters
   */
  clearAllFilters() {
    this.dataManager.clearFilters();

    // Reset UI elements
    if (this.elements.searchInput) this.elements.searchInput.value = '';
    if (this.elements.categoryFilter) this.elements.categoryFilter.value = '';
    if (this.elements.statusFilter) this.elements.statusFilter.value = '';

    this.updateSearchClear();
    this.render();
    this.saveUserPreferences();
  }

  /**
   * Handle view change
   */
  handleViewChange(view) {
    if (CONFIG.VIEWS.AVAILABLE_VIEWS.includes(view)) {
      this.currentView = view;
      this.updateViewMode();
      this.renderData();
    }
  }

  /**
   * Handle items per page change
   */
  handleItemsPerPageChange(itemsPerPage) {
    this.dataManager.setItemsPerPage(itemsPerPage);
    this.render();
  }

  /**
   * Go to specific page
   */
  goToPage(page) {
    this.dataManager.goToPage(page);
    this.render();
  }

  /**
   * Handle export
   */
  handleExport() {
    const format = 'json'; // Default format, could be made configurable
    const data = this.dataManager.exportData(format);
    const filename = `${CONFIG.EXPORT.FILENAME_PREFIX}_${new Date().toISOString().split('T')[0]}.${format}`;

    this.downloadFile(data, filename, format === 'json' ? 'application/json' : 'text/csv');
  }

  /**
   * Download file
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Handle refresh
   */
  async handleRefresh() {
    this.showLoading();
    const success = await this.dataManager.loadData();

    if (success) {
      this.populateFilters();
      this.render();
    } else {
      this.showError(this.dataManager.lastError);
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeyboardShortcuts(e) {
    // Don't handle shortcuts when modal is open or user is typing
    if (this.isModalOpen || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      if (e.key === 'Escape' && this.isModalOpen) {
        this.closeModal();
      }
      return;
    }

    switch (e.key) {
      case 'f':
      case 'F':
        if (e.ctrlKey) {
          e.preventDefault();
          this.elements.searchInput?.focus();
        }
        break;
      case 'n':
      case 'N':
        if (e.ctrlKey) {
          e.preventDefault();
          this.openAddModal();
        }
        break;
      case 'e':
      case 'E':
        if (e.ctrlKey) {
          e.preventDefault();
          this.handleExport();
        }
        break;
      case 'F5':
        e.preventDefault();
        this.handleRefresh();
        break;
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Could implement responsive adjustments here
    console.log('Window resized');
  }

  /**
   * Main render method
   */
  render() {
    if (this.dataManager.isLoading) {
      this.showLoading();
      return;
    }

    if (this.dataManager.filteredParts.length === 0) {
      if (this.dataManager.parts.length === 0) {
        this.showError('No data available');
      } else {
        this.showEmpty();
      }
      return;
    }

    this.showData();
    this.renderData();
    this.renderPagination();
    this.renderResultsInfo();
  }

  /**
   * Render data based on current view
   */
  renderData() {
    if (!this.elements.dataDisplay) return;

    const data = this.dataManager.getCurrentPageData();

    switch (this.currentView) {
      case 'grid':
        this.renderGridView(data);
        break;
      case 'list':
        this.renderListView(data);
        break;
      case 'card':
        this.renderCardView(data);
        break;
      case 'compact':
        this.renderCompactView(data);
        break;
      default:
        this.renderGridView(data);
    }
  }

  /**
   * Render grid view
   */
  renderGridView(data) {
    const html = data.map(part => `
      <div class="part-card" data-part-id="${part.partId}">
        <div class="part-card-header">
          <div class="part-id">${this.escapeHtml(part.partId)}</div>
          <div class="part-status">
            ${part.isActive ? '<span class="status-badge active">Active</span>' : '<span class="status-badge inactive">Inactive</span>'}
            ${part.isComponent ? '<span class="status-badge component">Component</span>' : ''}
          </div>
        </div>
        <div class="part-description">${this.escapeHtml(part.description)}</div>
        <div class="part-details">
          <div class="part-detail">
            <div class="part-detail-label">Category</div>
            <div class="part-detail-value">${this.escapeHtml(part.category || 'N/A')}</div>
          </div>
          <div class="part-detail">
            <div class="part-detail-label">UOM</div>
            <div class="part-detail-value">${this.escapeHtml(part.uom || 'N/A')}</div>
          </div>
          <div class="part-detail">
            <div class="part-detail-label">Function Group</div>
            <div class="part-detail-value">${this.escapeHtml(part.functionGroup || 'N/A')}</div>
          </div>
        </div>
      </div>
    `).join('');

    this.elements.dataDisplay.innerHTML = html;
  }

  /**
   * Render list view
   */
  renderListView(data) {
    const html = data.map(part => `
      <div class="part-list-item" data-part-id="${part.partId}">
        <div class="part-id">${this.escapeHtml(part.partId)}</div>
        <div class="part-list-main">
          <div class="part-list-title">
            <strong>${this.escapeHtml(part.description)}</strong>
            <div class="part-status">
              ${part.isActive ? '<span class="status-badge active">Active</span>' : '<span class="status-badge inactive">Inactive</span>'}
              ${part.isComponent ? '<span class="status-badge component">Component</span>' : ''}
            </div>
          </div>
          <div class="part-list-description">
            Category: ${this.escapeHtml(part.category || 'N/A')} |
            Function Group: ${this.escapeHtml(part.functionGroup || 'N/A')}
          </div>
          <div class="part-list-meta">
            <span>UOM: ${this.escapeHtml(part.uom || 'N/A')}</span>
            <span>Modified: ${this.formatDate(part.lastModified)}</span>
          </div>
        </div>
        <div class="part-actions">
          <button class="btn btn-secondary btn-sm" onclick="ui.editPart('${part.partId}')">Edit</button>
        </div>
      </div>
    `).join('');

    this.elements.dataDisplay.innerHTML = html;
  }

  /**
   * Render card view
   */
  renderCardView(data) {
    const html = data.map(part => `
      <div class="part-card-large" data-part-id="${part.partId}">
        <div class="part-card-large-header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div class="part-id" style="background: rgba(255,255,255,0.2); color: white;">${this.escapeHtml(part.partId)}</div>
            <div class="part-status">
              ${part.isActive ? '<span class="status-badge active">Active</span>' : '<span class="status-badge inactive">Inactive</span>'}
            </div>
          </div>
          <h3 style="margin: 0.5rem 0 0 0; font-size: 1.1rem;">${this.escapeHtml(part.description)}</h3>
        </div>
        <div class="part-card-large-body">
          <div class="part-details" style="grid-template-columns: 1fr;">
            <div class="part-detail">
              <div class="part-detail-label">Category</div>
              <div class="part-detail-value">${this.escapeHtml(part.category || 'N/A')}</div>
            </div>
            <div class="part-detail">
              <div class="part-detail-label">Function Group</div>
              <div class="part-detail-value">${this.escapeHtml(part.functionGroup || 'N/A')}</div>
            </div>
            <div class="part-detail">
              <div class="part-detail-label">Unit of Measure</div>
              <div class="part-detail-value">${this.escapeHtml(part.uom || 'N/A')}</div>
            </div>
          </div>
        </div>
        <div class="part-card-large-footer">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-size: 0.75rem; color: var(--gray-600);">
              ${part.isComponent ? 'Component' : 'Standard Part'} |
              Modified: ${this.formatDate(part.lastModified)}
            </div>
            <button class="btn btn-primary btn-sm" onclick="ui.editPart('${part.partId}')">Edit</button>
          </div>
        </div>
      </div>
    `).join('');

    this.elements.dataDisplay.innerHTML = html;
  }

  /**
   * Render compact view (table)
   */
  renderCompactView(data) {
    const html = `
      <table class="parts-table">
        <thead>
          <tr>
            <th>Part ID</th>
            <th>Description</th>
            <th>Category</th>
            <th>Function Group</th>
            <th>UOM</th>
            <th>Component</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          ${data.map(part => `
            <tr data-part-id="${part.partId}">
              <td class="part-id-cell">${this.escapeHtml(part.partId)}</td>
              <td class="description-cell" title="${this.escapeHtml(part.description)}">${this.escapeHtml(part.description)}</td>
              <td>${this.escapeHtml(part.category || 'N/A')}</td>
              <td>${this.escapeHtml(part.functionGroup || 'N/A')}</td>
              <td>${this.escapeHtml(part.uom || 'N/A')}</td>
              <td>${part.isComponent ? 'Yes' : 'No'}</td>
              <td>
                ${part.isActive ? '<span class="status-badge active">Active</span>' : '<span class="status-badge inactive">Inactive</span>'}
              </td>
              <td>
                <button class="btn btn-secondary btn-sm" onclick="ui.editPart('${part.partId}')">Edit</button>
              </td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;

    this.elements.dataDisplay.innerHTML = html;
  }

  /**
   * Render pagination controls
   */
  renderPagination() {
    const paginationInfo = this.dataManager.getPaginationInfo();

    // Update items per page selector
    if (this.elements.itemsPerPage) {
      this.elements.itemsPerPage.value = paginationInfo.itemsPerPage;
    }

    // Update pagination buttons
    if (this.elements.firstPageBtn) {
      this.elements.firstPageBtn.disabled = !paginationInfo.hasPrevPage;
    }
    if (this.elements.prevPageBtn) {
      this.elements.prevPageBtn.disabled = !paginationInfo.hasPrevPage;
    }
    if (this.elements.nextPageBtn) {
      this.elements.nextPageBtn.disabled = !paginationInfo.hasNextPage;
    }
    if (this.elements.lastPageBtn) {
      this.elements.lastPageBtn.disabled = !paginationInfo.hasNextPage;
    }

    // Render page numbers
    this.renderPageNumbers(paginationInfo);

    // Update pagination summary
    if (this.elements.paginationSummary) {
      this.elements.paginationSummary.textContent =
        `${paginationInfo.startItem}-${paginationInfo.endItem} of ${paginationInfo.totalItems}`;
    }
  }

  /**
   * Render page numbers
   */
  renderPageNumbers(paginationInfo) {
    if (!this.elements.pageNumbers) return;

    const { currentPage, totalPages } = paginationInfo;
    const maxVisible = CONFIG.PAGINATION.MAX_PAGE_NUMBERS_SHOWN;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let endPage = Math.min(totalPages, startPage + maxVisible - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1);
    }

    const pageNumbers = [];

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(`
        <button class="page-number ${i === currentPage ? 'active' : ''}"
                onclick="ui.goToPage(${i})"
                aria-label="Go to page ${i}"
                ${i === currentPage ? 'aria-current="page"' : ''}>
          ${i}
        </button>
      `);
    }

    this.elements.pageNumbers.innerHTML = pageNumbers.join('');
  }

  /**
   * Render results info
   */
  renderResultsInfo() {
    if (!this.elements.resultsInfo) return;

    const paginationInfo = this.dataManager.getPaginationInfo();
    const stats = this.dataManager.getStatistics();

    const resultsCount = document.querySelector('.results-count');
    const resultsRange = document.querySelector('.results-range');

    if (resultsCount) {
      resultsCount.textContent = `${stats.filtered} results`;
      if (stats.filtered !== stats.total) {
        resultsCount.textContent += ` (filtered from ${stats.total})`;
      }
    }

    if (resultsRange) {
      resultsRange.textContent = `Showing ${paginationInfo.startItem}-${paginationInfo.endItem}`;
    }
  }

  /**
   * Toggle FAB menu
   */
  toggleFab() {
    this.isFabOpen = !this.isFabOpen;

    if (this.elements.fabMain) {
      this.elements.fabMain.setAttribute('aria-expanded', this.isFabOpen.toString());
    }

    if (this.elements.fabMenu) {
      if (this.isFabOpen) {
        this.elements.fabMenu.removeAttribute('hidden');
      } else {
        setTimeout(() => {
          this.elements.fabMenu.setAttribute('hidden', '');
        }, CONFIG.FAB.ANIMATION_DURATION);
      }
    }
  }

  /**
   * Handle FAB action
   */
  handleFabAction(action) {
    switch (action) {
      case 'add':
        this.openAddModal();
        break;
      case 'export':
        this.handleExport();
        break;
      case 'search':
        this.elements.searchInput?.focus();
        break;
      case 'top':
        window.scrollTo({ top: 0, behavior: 'smooth' });
        break;
    }

    this.toggleFab();
  }

  /**
   * Open add modal
   */
  openAddModal() {
    this.resetForm();

    if (this.elements.modalTitle) {
      this.elements.modalTitle.textContent = 'Add New Part';
    }

    if (this.elements.modalSave) {
      this.elements.modalSave.textContent = 'Save Part';
    }

    this.showModal();
  }

  /**
   * Open edit modal
   */
  editPart(partId) {
    const part = this.dataManager.parts.find(p => p.partId === partId);
    if (!part) return;

    this.resetForm();
    this.populateForm(part);

    if (this.elements.modalTitle) {
      this.elements.modalTitle.textContent = 'Edit Part';
    }

    if (this.elements.modalSave) {
      this.elements.modalSave.textContent = 'Update Part';
    }

    this.showModal();
  }

  /**
   * Show modal
   */
  showModal() {
    if (this.elements.modalOverlay) {
      this.elements.modalOverlay.removeAttribute('hidden');
      this.elements.modalOverlay.setAttribute('aria-hidden', 'false');
    }

    this.isModalOpen = true;

    // Focus first input
    setTimeout(() => {
      this.elements.partId?.focus();
    }, CONFIG.UI.MODAL_ANIMATION_DURATION);
  }

  /**
   * Close modal
   */
  closeModal() {
    if (this.elements.modalOverlay) {
      this.elements.modalOverlay.setAttribute('aria-hidden', 'true');

      setTimeout(() => {
        this.elements.modalOverlay.setAttribute('hidden', '');
      }, CONFIG.UI.MODAL_ANIMATION_DURATION);
    }

    this.isModalOpen = false;
  }

  /**
   * Reset form
   */
  resetForm() {
    if (this.elements.partForm) {
      this.elements.partForm.reset();
    }

    // Clear validation errors
    document.querySelectorAll('.form-error').forEach(el => {
      el.setAttribute('hidden', '');
      el.textContent = '';
    });

    // Reset form fields
    if (this.elements.partId) this.elements.partId.value = '';
    if (this.elements.partDescription) this.elements.partDescription.value = '';
    if (this.elements.partCategory) this.elements.partCategory.value = '';
    if (this.elements.partUom) this.elements.partUom.value = '';
    if (this.elements.partFunctionGroup) this.elements.partFunctionGroup.value = '';
    if (this.elements.isComponent) this.elements.isComponent.checked = false;
    if (this.elements.isActive) this.elements.isActive.checked = true;
  }

  /**
   * Populate form with part data
   */
  populateForm(part) {
    if (this.elements.partId) {
      this.elements.partId.value = part.partId || '';
      this.elements.partId.readOnly = true; // Don't allow editing part ID
    }

    if (this.elements.partDescription) {
      this.elements.partDescription.value = part.description || '';
    }

    if (this.elements.partCategory) {
      this.elements.partCategory.value = part.category || '';
    }

    if (this.elements.partUom) {
      this.elements.partUom.value = part.uom || '';
    }

    if (this.elements.partFunctionGroup) {
      this.elements.partFunctionGroup.value = part.functionGroup || '';
    }

    if (this.elements.isComponent) {
      this.elements.isComponent.checked = part.isComponent || false;
    }

    if (this.elements.isActive) {
      this.elements.isActive.checked = part.isActive !== false; // Default to true
    }
  }

  /**
   * Handle form submit
   */
  handleFormSubmit() {
    if (!this.validateForm()) return;

    const formData = this.getFormData();
    const isEdit = this.elements.partId?.readOnly;

    let success;
    if (isEdit) {
      success = this.dataManager.updatePart(formData.partId, formData);
    } else {
      success = this.dataManager.addPart(formData);
    }

    if (success) {
      this.closeModal();
      this.render();
    } else {
      this.showFormError('general', this.dataManager.lastError || 'An error occurred while saving the part.');
    }
  }

  /**
   * Validate form
   */
  validateForm() {
    let isValid = true;

    // Part ID validation
    if (this.elements.partId && !this.elements.partId.readOnly) {
      const partId = this.elements.partId.value.trim();
      const validation = CONFIG.VALIDATION.PART_ID;

      if (validation.REQUIRED && !partId) {
        this.showFormError('part-id', validation.ERROR_MESSAGES.REQUIRED);
        isValid = false;
      } else if (partId.length < validation.MIN_LENGTH) {
        this.showFormError('part-id', validation.ERROR_MESSAGES.MIN_LENGTH);
        isValid = false;
      } else if (partId.length > validation.MAX_LENGTH) {
        this.showFormError('part-id', validation.ERROR_MESSAGES.MAX_LENGTH);
        isValid = false;
      } else if (validation.PATTERN && !validation.PATTERN.test(partId)) {
        this.showFormError('part-id', validation.ERROR_MESSAGES.PATTERN);
        isValid = false;
      }
    }

    // Description validation
    if (this.elements.partDescription) {
      const description = this.elements.partDescription.value.trim();
      const validation = CONFIG.VALIDATION.DESCRIPTION;

      if (validation.REQUIRED && !description) {
        this.showFormError('part-description', validation.ERROR_MESSAGES.REQUIRED);
        isValid = false;
      } else if (description.length < validation.MIN_LENGTH) {
        this.showFormError('part-description', validation.ERROR_MESSAGES.MIN_LENGTH);
        isValid = false;
      } else if (description.length > validation.MAX_LENGTH) {
        this.showFormError('part-description', validation.ERROR_MESSAGES.MAX_LENGTH);
        isValid = false;
      }
    }

    // Category validation
    if (this.elements.partCategory) {
      const category = this.elements.partCategory.value;
      const validation = CONFIG.VALIDATION.CATEGORY;

      if (validation.REQUIRED && !category) {
        this.showFormError('part-category', validation.ERROR_MESSAGES.REQUIRED);
        isValid = false;
      }
    }

    return isValid;
  }

  /**
   * Show form error
   */
  showFormError(fieldId, message) {
    const errorElement = document.getElementById(`${fieldId}-error`);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.removeAttribute('hidden');
    }
  }

  /**
   * Get form data
   */
  getFormData() {
    return {
      partId: this.elements.partId?.value.trim(),
      description: this.elements.partDescription?.value.trim(),
      category: this.elements.partCategory?.value,
      uom: this.elements.partUom?.value.trim(),
      functionGroup: this.elements.partFunctionGroup?.value.trim(),
      isComponent: this.elements.isComponent?.checked,
      isActive: this.elements.isActive?.checked
    };
  }

  /**
   * Format date
   */
  formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return 'Invalid Date';
    }
  }

  /**
   * Escape HTML
   */
  escapeHtml(text) {
    if (!text) return '';

    return text.toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
}
