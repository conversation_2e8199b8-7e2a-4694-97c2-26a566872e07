/**
 * Application Configuration
 * All configurable values and constants for the Parts Management System
 */

export const CONFIG = {
  // ===== APPLICATION SETTINGS =====
  APP: {
    NAME: 'Parts Management System',
    VERSION: '1.0.0',
    DESCRIPTION: 'Modern parts inventory management application',
    AUTHOR: 'Parts Manager Team'
  },

  // ===== DATA SETTINGS =====
  DATA: {
    JSON_FILE_PATH: 'data/parts.json',
    BACKUP_STORAGE_KEY: 'parts_backup_data',
    LAST_UPDATED_KEY: 'parts_last_updated',
    AUTO_SAVE_INTERVAL: 30000, // 30 seconds
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
  },

  // ===== PAGINATION SETTINGS =====
  PAGINATION: {
    DEFAULT_ITEMS_PER_PAGE: 25,
    ITEMS_PER_PAGE_OPTIONS: [10, 25, 50, 100],
    MAX_PAGE_NUMBERS_SHOWN: 5,
    ENABLE_FIRST_LAST_BUTTONS: true,
    ENABLE_PREV_NEXT_BUTTONS: true
  },

  // ===== VIEW SETTINGS =====
  VIEWS: {
    DEFAULT_VIEW: 'grid',
    AVAILABLE_VIEWS: ['grid', 'list', 'card', 'compact'],
    VIEW_STORAGE_KEY: 'preferred_view_mode',
    ANIMATION_DURATION: 300, // milliseconds
    CARD_DESCRIPTION_MAX_LENGTH: 150,
    LIST_DESCRIPTION_MAX_LENGTH: 200
  },

  // ===== SEARCH AND FILTER SETTINGS =====
  SEARCH: {
    DEBOUNCE_DELAY: 300, // milliseconds
    MIN_SEARCH_LENGTH: 2,
    SEARCH_FIELDS: ['partId', 'description', 'category', 'functionGroup'],
    HIGHLIGHT_MATCHES: true,
    CASE_SENSITIVE: false,
    STORAGE_KEY: 'last_search_query'
  },

  // ===== FILTER SETTINGS =====
  FILTERS: {
    CATEGORY_FILTER_KEY: 'category_filter',
    STATUS_FILTER_KEY: 'status_filter',
    STORAGE_KEY: 'applied_filters',
    AUTO_POPULATE_CATEGORIES: true,
    SHOW_ITEM_COUNTS: true
  },

  // ===== EXPORT SETTINGS =====
  EXPORT: {
    DEFAULT_FORMAT: 'json',
    AVAILABLE_FORMATS: ['json', 'csv', 'xlsx'],
    FILENAME_PREFIX: 'parts_export',
    INCLUDE_TIMESTAMP: true,
    CSV_DELIMITER: ',',
    CSV_QUOTE_CHAR: '"',
    EXCEL_SHEET_NAME: 'Parts Data'
  },

  // ===== UI SETTINGS =====
  UI: {
    THEME_STORAGE_KEY: 'preferred_theme',
    DEFAULT_THEME: 'light',
    AVAILABLE_THEMES: ['light', 'dark', 'auto'],
    SHOW_LOADING_SPINNER: true,
    LOADING_MIN_DURATION: 500, // minimum time to show loading
    TOAST_DURATION: 4000, // 4 seconds
    MODAL_ANIMATION_DURATION: 250,
    FAB_ANIMATION_DURATION: 200
  },

  // ===== FLOATING ACTION BUTTON SETTINGS =====
  FAB: {
    ENABLED: true,
    POSITION: 'bottom-right',
    SHOW_LABELS: true,
    AUTO_HIDE_ON_SCROLL: false,
    SCROLL_THRESHOLD: 100, // pixels
    ACTIONS: [
      { id: 'add', label: 'Add Part', icon: '➕', action: 'openAddModal' },
      { id: 'export', label: 'Export', icon: '📤', action: 'exportData' },
      { id: 'search', label: 'Search', icon: '🔍', action: 'focusSearch' },
      { id: 'top', label: 'Top', icon: '⬆️', action: 'scrollToTop' }
    ]
  },

  // ===== FORM VALIDATION SETTINGS =====
  VALIDATION: {
    PART_ID: {
      REQUIRED: true,
      MIN_LENGTH: 3,
      MAX_LENGTH: 20,
      PATTERN: /^[A-Z0-9]+$/,
      ERROR_MESSAGES: {
        REQUIRED: 'Part ID is required',
        MIN_LENGTH: 'Part ID must be at least 3 characters',
        MAX_LENGTH: 'Part ID cannot exceed 20 characters',
        PATTERN: 'Part ID must contain only uppercase letters and numbers'
      }
    },
    DESCRIPTION: {
      REQUIRED: true,
      MIN_LENGTH: 10,
      MAX_LENGTH: 500,
      ERROR_MESSAGES: {
        REQUIRED: 'Description is required',
        MIN_LENGTH: 'Description must be at least 10 characters',
        MAX_LENGTH: 'Description cannot exceed 500 characters'
      }
    },
    CATEGORY: {
      REQUIRED: true,
      ERROR_MESSAGES: {
        REQUIRED: 'Category is required'
      }
    }
  },

  // ===== ACCESSIBILITY SETTINGS =====
  ACCESSIBILITY: {
    ENABLE_KEYBOARD_NAVIGATION: true,
    ENABLE_SCREEN_READER_SUPPORT: true,
    FOCUS_VISIBLE_OUTLINE: true,
    HIGH_CONTRAST_MODE: false,
    REDUCED_MOTION_RESPECT: true,
    ARIA_LIVE_REGIONS: true
  },

  // ===== PERFORMANCE SETTINGS =====
  PERFORMANCE: {
    VIRTUAL_SCROLLING_THRESHOLD: 1000, // items
    DEBOUNCE_RESIZE: 250, // milliseconds
    LAZY_LOAD_IMAGES: true,
    CACHE_SEARCH_RESULTS: true,
    MAX_CACHED_SEARCHES: 10,
    PRELOAD_NEXT_PAGE: true
  },

  // ===== ERROR HANDLING SETTINGS =====
  ERROR_HANDLING: {
    SHOW_DETAILED_ERRORS: false, // set to true for development
    LOG_ERRORS_TO_CONSOLE: true,
    RETRY_FAILED_REQUESTS: true,
    FALLBACK_TO_LOCAL_STORAGE: true,
    ERROR_TOAST_DURATION: 6000 // 6 seconds
  },

  // ===== DEVELOPMENT SETTINGS =====
  DEVELOPMENT: {
    DEBUG_MODE: false,
    MOCK_API_DELAY: 500, // milliseconds
    ENABLE_PERFORMANCE_MONITORING: false,
    LOG_LEVEL: 'warn', // 'debug', 'info', 'warn', 'error'
    SHOW_COMPONENT_BOUNDARIES: false
  },

  // ===== CATEGORIES =====
  CATEGORIES: [
    'AUT-Classic/New',
    'LFS 96-2000',
    'LFS 2000-2007',
    'Engine',
    'ZBUY TOBE DETE'
  ],

  // ===== FUNCTION GROUPS =====
  FUNCTION_GROUPS: [
    'Each',
    'Piece',
    'Engine'
  ],

  // ===== UNITS OF MEASURE =====
  UNITS_OF_MEASURE: [
    'Each',
    'Piece',
    'Set',
    'Kit',
    'Meter',
    'Liter',
    'Kilogram',
    'Gram'
  ],

  // ===== STATUS OPTIONS =====
  STATUS_OPTIONS: {
    ACTIVE: { value: true, label: 'Active', color: 'success' },
    INACTIVE: { value: false, label: 'Inactive', color: 'error' }
  },

  // ===== COMPONENT OPTIONS =====
  COMPONENT_OPTIONS: {
    YES: { value: true, label: 'Yes', color: 'warning' },
    NO: { value: false, label: 'No', color: 'info' }
  },

  // ===== API ENDPOINTS (for future use) =====
  API: {
    BASE_URL: '/api/v1',
    ENDPOINTS: {
      PARTS: '/parts',
      CATEGORIES: '/categories',
      EXPORT: '/export',
      IMPORT: '/import',
      SEARCH: '/search'
    },
    TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3
  },

  // ===== KEYBOARD SHORTCUTS =====
  KEYBOARD_SHORTCUTS: {
    SEARCH: 'Ctrl+F',
    ADD_NEW: 'Ctrl+N',
    EXPORT: 'Ctrl+E',
    REFRESH: 'F5',
    ESCAPE: 'Escape',
    ENTER: 'Enter',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    PAGE_UP: 'PageUp',
    PAGE_DOWN: 'PageDown'
  },

  // ===== RESPONSIVE BREAKPOINTS =====
  BREAKPOINTS: {
    MOBILE: 320,
    TABLET: 768,
    DESKTOP: 1024,
    LARGE_DESKTOP: 1200
  }
};

// Freeze the configuration to prevent accidental modifications
Object.freeze(CONFIG);

export default CONFIG;
