/* ===== CSS CUSTOM PROPERTIES (VARIABLES) ===== */
:root {
  /* Colors */
  --primary-color: #000000;
  --primary-hover: #000000;
  --primary-light: #000000;
  --secondary-color: #64748b;
  --secondary-hover: #475569;
  --accent-color: #f59e0b;
  --accent-hover: #d97706;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Typography */
  --font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Layout */
  --container-max-width: 1200px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== RESET AND BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  background-color: var(--gray-50);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* ===== HEADER ===== */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  gap: var(--spacing-lg);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
}

.logo-icon {
  font-size: var(--font-size-2xl);
}

/* ===== NAVIGATION ===== */
.nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.view-toggle {
  display: flex;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  gap: var(--spacing-xs);
}

.view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  background: transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.view-btn:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

.view-btn.active {
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.view-icon {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-icon {
  font-size: var(--font-size-base);
}

/* ===== SEARCH SECTION ===== */
.search-section {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-md) 0;
}

.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-clear {
  position: absolute;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.search-clear:hover {
  color: var(--gray-600);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background: var(--white);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* ===== MAIN CONTENT ===== */
.main {
  flex: 1;
  padding: var(--spacing-xl) 0;
}

/* ===== STATES ===== */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-md);
  min-height: 300px;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
}

.error-state h2,
.empty-state h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-sm);
}

.error-state p,
.empty-state p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
}

/* ===== DATA CONTAINER ===== */
.data-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--gray-200);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.results-count {
  font-weight: var(--font-weight-medium);
  color: var(--gray-800);
}

/* ===== DATA DISPLAY VIEWS ===== */
.data-display {
  transition: all var(--transition-normal);
}

/* Grid View */
.data-display.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.part-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.part-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.part-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.part-id {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  background: var(--primary-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.part-status {
  display: flex;
  gap: var(--spacing-xs);
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #991b1b;
}

.status-badge.component {
  background: #fef3c7;
  color: #92400e;
}

.part-description {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.part-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.part-detail {
  display: flex;
  flex-direction: column;
}

.part-detail-label {
  color: var(--gray-500);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: var(--spacing-xs);
}

.part-detail-value {
  color: var(--gray-800);
  font-weight: var(--font-weight-medium);
}

/* List View */
.data-display.list-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.part-list-item {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-lg);
  align-items: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.part-list-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.part-list-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.part-list-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.part-list-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.part-list-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* Card View */
.data-display.card-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.part-card-large {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.part-card-large:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.part-card-large-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--white);
  padding: var(--spacing-lg);
}

.part-card-large-body {
  padding: var(--spacing-lg);
}

.part-card-large-footer {
  background: var(--gray-50);
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

/* Compact View */
.data-display.compact-view {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.parts-table {
  width: 100%;
  border-collapse: collapse;
}

.parts-table th,
.parts-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.parts-table th {
  background: var(--gray-50);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  position: sticky;
  top: 0;
  z-index: 10;
}

.parts-table tbody tr {
  transition: background-color var(--transition-fast);
  cursor: pointer;
}

.parts-table tbody tr:hover {
  background: var(--gray-50);
}

.parts-table .part-id-cell {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

.parts-table .description-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ===== PAGINATION ===== */
.footer {
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--spacing-md) 0;
  margin-top: auto;
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.pagination-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--white);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--gray-300);
  background: var(--white);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: var(--spacing-xs);
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--gray-300);
  background: var(--white);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.page-number:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.page-number.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.pagination-summary {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* ===== FLOATING ACTION BUTTON ===== */
.fab-container {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: var(--z-fixed);
}

.fab-main {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.fab-main:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.fab-main[aria-expanded="true"] {
  transform: rotate(45deg);
}

.fab-menu {
  position: absolute;
  bottom: 4.5rem;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
}

.fab-menu:not([hidden]) {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.fab-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
  white-space: nowrap;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.fab-item:hover {
  background: var(--gray-50);
  border-color: var(--primary-color);
  transform: translateX(-4px);
}

.fab-item-icon {
  font-size: var(--font-size-base);
}

.fab-item-label {
  font-weight: var(--font-weight-medium);
}

/* ===== MODAL ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: opacity var(--transition-normal);
  padding: var(--spacing-md);
}

.modal-overlay:not([hidden]) {
  opacity: 1;
}

.modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal-overlay:not([hidden]) .modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.modal-close {
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* ===== FORMS ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
  background: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-input:invalid {
  border-color: var(--error-color);
}

.form-error {
  color: var(--error-color);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.form-checkbox {
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary-color);
}

.checkbox-text {
  color: var(--gray-700);
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-mono {
  font-family: var(--font-family-mono);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}
