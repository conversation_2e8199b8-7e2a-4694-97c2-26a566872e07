<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern Parts Management System - Manage and view parts inventory with multiple view modes">
    <title>Parts Management System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="data/parts.json" as="fetch" crossorigin="anonymous">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header with Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <span class="logo-icon">⚙️</span>
                    Parts Manager
                </h1>
                
                <!-- Main Navigation -->
                <nav class="nav" role="navigation" aria-label="Main navigation">
                    <div class="nav-actions">
                        <button class="btn btn-primary" id="add-btn" aria-label="Add new part">
                            <span class="btn-icon">➕</span>
                            Add
                        </button>
                        <button class="btn btn-secondary" id="export-btn" aria-label="Export data">
                            <span class="btn-icon">📤</span>
                            Export
                        </button>
                        <button class="btn btn-secondary" id="refresh-btn" aria-label="Refresh data">
                            <span class="btn-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                    
                    <!-- View Mode Toggle -->
                    <div class="view-toggle" role="radiogroup" aria-label="View mode selection">
                        <button class="view-btn active" data-view="grid" aria-label="Grid view" aria-pressed="true">
                            <span class="view-icon">⊞</span>
                        </button>
                        <button class="view-btn" data-view="list" aria-label="List view" aria-pressed="false">
                            <span class="view-icon">☰</span>
                        </button>
                        <button class="view-btn" data-view="card" aria-label="Card view" aria-pressed="false">
                            <span class="view-icon">⊡</span>
                        </button>
                        <button class="view-btn" data-view="compact" aria-label="Compact view" aria-pressed="false">
                            <span class="view-icon">≡</span>
                        </button>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Search and Filter Section -->
    <section class="search-section" aria-label="Search and filter">
        <div class="container">
            <div class="search-controls">
                <div class="search-input-wrapper">
                    <input 
                        type="search" 
                        id="search-input" 
                        class="search-input" 
                        placeholder="Search parts by ID, description, or category..."
                        aria-label="Search parts"
                        autocomplete="off"
                    >
                    <button class="search-clear" id="search-clear" aria-label="Clear search" hidden>
                        <span>✕</span>
                    </button>
                </div>
                
                <div class="filter-controls">
                    <select id="category-filter" class="filter-select" aria-label="Filter by category">
                        <option value="">All Categories</option>
                    </select>
                    
                    <select id="status-filter" class="filter-select" aria-label="Filter by status">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content Area -->
    <main id="main-content" class="main" role="main">
        <div class="container">
            <!-- Loading State -->
            <div class="loading-state" id="loading-state" aria-live="polite">
                <div class="loading-spinner"></div>
                <p>Loading parts data...</p>
            </div>
            
            <!-- Error State -->
            <div class="error-state" id="error-state" hidden aria-live="assertive">
                <div class="error-icon">⚠️</div>
                <h2>Error Loading Data</h2>
                <p id="error-message">Unable to load parts data. Please try refreshing the page.</p>
                <button class="btn btn-primary" id="retry-btn">Retry</button>
            </div>
            
            <!-- Empty State -->
            <div class="empty-state" id="empty-state" hidden aria-live="polite">
                <div class="empty-icon">📦</div>
                <h2>No Parts Found</h2>
                <p>No parts match your current search criteria.</p>
                <button class="btn btn-primary" id="clear-filters-btn">Clear Filters</button>
            </div>
            
            <!-- Data Display Area -->
            <div class="data-container" id="data-container" hidden>
                <!-- Results Info -->
                <div class="results-info" id="results-info" aria-live="polite">
                    <span class="results-count"></span>
                    <span class="results-range"></span>
                </div>
                
                <!-- Data Grid/List/Cards -->
                <div class="data-display" id="data-display" role="region" aria-label="Parts data">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer with Pagination -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="pagination-container">
                <div class="pagination-info">
                    <label for="items-per-page" class="pagination-label">Items per page:</label>
                    <select id="items-per-page" class="pagination-select">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
                
                <div class="pagination-controls" id="pagination-controls">
                    <button class="pagination-btn" id="first-page" aria-label="First page" disabled>
                        <span>⏮️</span>
                    </button>
                    <button class="pagination-btn" id="prev-page" aria-label="Previous page" disabled>
                        <span>⏪</span>
                    </button>
                    
                    <div class="page-numbers" id="page-numbers" role="navigation" aria-label="Page navigation">
                        <!-- Dynamic page numbers -->
                    </div>
                    
                    <button class="pagination-btn" id="next-page" aria-label="Next page">
                        <span>⏩</span>
                    </button>
                    <button class="pagination-btn" id="last-page" aria-label="Last page">
                        <span>⏭️</span>
                    </button>
                </div>
                
                <div class="pagination-summary" id="pagination-summary" aria-live="polite">
                    <!-- Dynamic summary -->
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Action Button with Speed Dial -->
    <div class="fab-container" id="fab-container">
        <button class="fab-main" id="fab-main" aria-label="Quick actions menu" aria-expanded="false">
            <span class="fab-icon">⚡</span>
        </button>
        
        <div class="fab-menu" id="fab-menu" hidden>
            <button class="fab-item" data-action="add" aria-label="Quick add part">
                <span class="fab-item-icon">➕</span>
                <span class="fab-item-label">Add Part</span>
            </button>
            <button class="fab-item" data-action="export" aria-label="Quick export">
                <span class="fab-item-icon">📤</span>
                <span class="fab-item-label">Export</span>
            </button>
            <button class="fab-item" data-action="search" aria-label="Focus search">
                <span class="fab-item-icon">🔍</span>
                <span class="fab-item-label">Search</span>
            </button>
            <button class="fab-item" data-action="top" aria-label="Scroll to top">
                <span class="fab-item-icon">⬆️</span>
                <span class="fab-item-label">Top</span>
            </button>
        </div>
    </div>

    <!-- Modal for Add/Edit Part -->
    <div class="modal-overlay" id="modal-overlay" hidden aria-hidden="true">
        <div class="modal" role="dialog" aria-labelledby="modal-title" aria-modal="true">
            <div class="modal-header">
                <h2 id="modal-title">Add New Part</h2>
                <button class="modal-close" id="modal-close" aria-label="Close modal">
                    <span>✕</span>
                </button>
            </div>
            
            <form class="modal-body" id="part-form" novalidate>
                <div class="form-group">
                    <label for="part-id" class="form-label">Part ID *</label>
                    <input type="text" id="part-id" class="form-input" required aria-describedby="part-id-error">
                    <div class="form-error" id="part-id-error" hidden></div>
                </div>
                
                <div class="form-group">
                    <label for="part-description" class="form-label">Description *</label>
                    <textarea id="part-description" class="form-input" rows="3" required aria-describedby="part-description-error"></textarea>
                    <div class="form-error" id="part-description-error" hidden></div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="part-category" class="form-label">Category *</label>
                        <select id="part-category" class="form-input" required aria-describedby="part-category-error">
                            <option value="">Select Category</option>
                        </select>
                        <div class="form-error" id="part-category-error" hidden></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="part-uom" class="form-label">UOM</label>
                        <input type="text" id="part-uom" class="form-input" placeholder="Each, Piece, etc.">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="part-function-group" class="form-label">Function Group</label>
                        <input type="text" id="part-function-group" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="is-component" class="form-checkbox">
                                <span class="checkbox-text">Is Component</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="is-active" class="form-checkbox" checked>
                                <span class="checkbox-text">Is Active</span>
                            </label>
                        </div>
                    </div>
                </div>
            </form>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modal-cancel">Cancel</button>
                <button type="submit" class="btn btn-primary" form="part-form" id="modal-save">Save Part</button>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script type="module" src="js/app.js"></script>
</body>
</html>
